# 📢 超简单公告系统使用说明

## 🎯 概述
这是一个极简的公告系统，您只需要在服务器上放一个JSON文件就能更新应用中的公告。

## 🚀 使用步骤

### 1. 上传公告文件到服务器
将 `announcement.json` 文件上传到您的服务器根目录，确保可以通过以下地址访问：
```
https://your-domain.com/announcement.json
```

### 2. 编辑公告内容
直接编辑服务器上的 `announcement.json` 文件：

```json
{
  "id": "2025-01-27-001",
  "title": "公告标题",
  "content": "公告内容\n\n支持换行和多段落文本。",
  "created_at": "2025-01-27T10:00:00Z",
  "is_important": false,
  "is_active": true
}
```

### 3. 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `id` | 字符串 | 公告唯一标识，修改后用户会重新看到公告 |
| `title` | 字符串 | 公告标题 |
| `content` | 字符串 | 公告内容，支持 `\n` 换行 |
| `created_at` | 字符串 | 创建时间（ISO格式） |
| `is_important` | 布尔值 | 是否为重要公告（红色显示） |
| `is_active` | 布尔值 | 是否激活公告 |

## 📝 使用示例

### 发布普通公告
```json
{
  "id": "update-2025-01-27",
  "title": "版本更新通知",
  "content": "应用已更新到 v4.2.6\n\n新功能：\n• 优化了角色选择界面\n• 修复了已知问题\n• 提升了生成速度\n\n请及时更新以获得最佳体验。",
  "created_at": "2025-01-27T14:30:00Z",
  "is_important": false,
  "is_active": true
}
```

### 发布重要公告
```json
{
  "id": "maintenance-2025-01-27",
  "title": "紧急维护通知",
  "content": "服务器将于今晚 22:00-24:00 进行维护\n\n维护内容：\n• 服务器升级\n• 数据库优化\n• 安全更新\n\n维护期间可能影响部分功能使用，请提前保存您的作品。",
  "created_at": "2025-01-27T16:00:00Z",
  "is_important": true,
  "is_active": true
}
```

### 关闭公告
```json
{
  "id": "any-id",
  "title": "任意标题",
  "content": "任意内容",
  "created_at": "2025-01-27T10:00:00Z",
  "is_important": false,
  "is_active": false
}
```

## 🔧 服务器配置

### 方法1：直接上传文件
1. 使用FTP、SFTP或服务器面板上传 `announcement.json`
2. 确保文件可以通过HTTP访问
3. 测试访问：`curl https://your-domain.com/announcement.json`

### 方法2：使用命令行
```bash
# SSH到服务器
ssh user@your-server

# 创建公告文件
cat > announcement.json << 'EOF'
{
  "id": "welcome-2025",
  "title": "欢迎使用岱宗文脉",
  "content": "感谢您的使用！",
  "created_at": "2025-01-27T10:00:00Z",
  "is_important": false,
  "is_active": true
}
EOF

# 设置正确的权限
chmod 644 announcement.json
```

### 方法3：使用Nginx静态文件服务
确保Nginx配置允许访问JSON文件：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    
    location /announcement.json {
        add_header Access-Control-Allow-Origin *;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
}
```

## 📱 客户端配置

客户端会自动从以下地址获取公告：
- 主地址：`https://dzwm.xyz/announcement.json`
- 备用地址：`http://47.120.19.139:8000/announcement.json`

如需修改地址，请编辑 `lib/config/api_config.dart` 文件。

## 🔍 测试验证

### 1. 测试文件访问
```bash
curl https://your-domain.com/announcement.json
```

### 2. 测试客户端
1. 在应用中点击侧边栏的"刷新公告"
2. 查看是否显示最新公告

### 3. 验证JSON格式
使用在线JSON验证器检查文件格式是否正确。

## 💡 使用技巧

### 1. 快速编辑
```bash
# 使用vim编辑
vim announcement.json

# 使用nano编辑
nano announcement.json
```

### 2. 一键发布脚本
创建 `update_announcement.sh`：
```bash
#!/bin/bash
cat > announcement.json << EOF
{
  "id": "$(date +%Y%m%d-%H%M%S)",
  "title": "$1",
  "content": "$2",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "is_important": ${3:-false},
  "is_active": true
}
EOF
echo "公告已更新: $1"
```

使用方法：
```bash
chmod +x update_announcement.sh
./update_announcement.sh "新版本发布" "请更新到最新版本" false
```

### 3. 定时公告
使用cron定时更新公告：
```bash
# 编辑crontab
crontab -e

# 每天9点发布提醒
0 9 * * * /path/to/update_announcement.sh "每日提醒" "记得备份您的重要作品"
```

## 🛠️ 故障排除

### 问题1：客户端无法获取公告
- 检查文件是否可以访问：`curl https://your-domain.com/announcement.json`
- 检查JSON格式是否正确
- 检查服务器CORS设置

### 问题2：公告不显示
- 确认 `is_active` 设置为 `true`
- 检查 `id` 是否与之前相同（相同ID不会重复显示）
- 在应用中手动点击"刷新公告"

### 问题3：JSON格式错误
- 使用JSON验证器检查格式
- 注意逗号、引号、括号的正确使用
- 确保文件编码为UTF-8

## 📞 支持

这个方案的优点：
✅ 极简单，只需要一个JSON文件
✅ 无需数据库和复杂API
✅ 修改后立即生效
✅ 支持所有静态文件服务器
✅ 易于备份和版本控制

现在您只需要：
1. 把 `announcement.json` 放到服务器上
2. 需要更新公告时直接编辑这个文件
3. 用户刷新后就能看到新公告
