import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

/// 公告服务 - 从服务器动态获取公告
class AnnouncementService extends GetxService {
  static const String _lastAnnouncementIdKey = 'last_announcement_id';
  static const String _cachedAnnouncementKey = 'cached_announcement';
  static const String _lastCheckTimeKey = 'last_announcement_check_time';

  final announcement = Rxn<Announcement>();
  final RxBool isLoading = false.obs;

  // 静态公告文件地址
  final String _announcementUrl = '${ApiConfig.baseUrl}/announcement.json';
  final String _backupAnnouncementUrl = '${ApiConfig.backupUrl}/announcement.json';

  // 检查间隔（小时）
  static const int _checkIntervalHours = 6;

  @override
  void onInit() {
    super.onInit();
    initAnnouncement();
  }

  /// 初始化公告服务
  Future<void> initAnnouncement() async {
    try {
      print('🔔 初始化公告服务...');
      
      // 检查是否需要获取新公告
      if (await _shouldCheckForNewAnnouncement()) {
        print('🔄 需要检查新公告');
        // 异步获取最新公告，不阻塞应用启动
        _fetchLatestAnnouncementAsync();
      } else {
        print('📋 加载缓存公告');
        // 加载缓存的公告（如果有未读的）
        await _loadCachedAnnouncement();
      }
    } catch (e) {
      print('❌ 初始化公告服务失败: $e');
    }
  }

  /// 检查是否应该获取新公告
  Future<bool> _shouldCheckForNewAnnouncement() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheckTime = prefs.getInt(_lastCheckTimeKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeDiff = now - lastCheckTime;
      final hoursDiff = timeDiff / (1000 * 60 * 60);
      
      return hoursDiff >= _checkIntervalHours;
    } catch (e) {
      print('检查时间间隔失败: $e');
      return true; // 出错时默认检查
    }
  }

  /// 加载缓存的公告
  Future<void> _loadCachedAnnouncement() async {
    try {
      final cachedAnnouncement = await _getCachedAnnouncement();
      if (cachedAnnouncement != null) {
        final prefs = await SharedPreferences.getInstance();
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);
        
        // 如果有缓存公告且未读，显示它
        if (lastAnnouncementId != cachedAnnouncement.id) {
          announcement.value = cachedAnnouncement;
          print('📢 显示缓存公告: ${cachedAnnouncement.title}');
        }
      }
    } catch (e) {
      print('加载缓存公告失败: $e');
    }
  }

  /// 异步获取最新公告
  Future<void> _fetchLatestAnnouncementAsync() async {
    try {
      isLoading.value = true;
      
      // 获取最新公告
      final latestAnnouncement = await _fetchLatestAnnouncement();
      
      if (latestAnnouncement != null) {
        print('✅ 获取到最新公告: ${latestAnnouncement.title}');
        
        // 保存到缓存
        await _saveCachedAnnouncement(latestAnnouncement);
        
        // 更新检查时间
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_lastCheckTimeKey, DateTime.now().millisecondsSinceEpoch);
        
        // 检查是否是新公告
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);
        if (lastAnnouncementId != latestAnnouncement.id) {
          announcement.value = latestAnnouncement;
          print('🆕 显示新公告');
        } else {
          print('📋 公告已读过');
        }
      } else {
        print('ℹ️ 没有获取到公告');
      }
    } catch (e) {
      print('❌ 获取公告失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 从静态文件获取最新公告
  Future<Announcement?> _fetchLatestAnnouncement() async {
    try {
      final headers = {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      };

      final client = http.Client();
      try {
        final timeout = Duration(seconds: ApiConfig.connectionTimeout);

        // 先尝试主域名
        try {
          final response = await client
              .get(Uri.parse(_announcementUrl), headers: headers)
              .timeout(timeout);

          if (response.statusCode == 200 && response.body.isNotEmpty) {
            final data = json.decode(response.body);
            // 检查公告是否激活
            if (data['is_active'] == true) {
              return Announcement.fromJson(data);
            }
          }
        } catch (e) {
          print('主域名获取公告失败，尝试备用地址: $e');

          // 如果主域名失败，尝试备用地址
          final response = await client
              .get(Uri.parse(_backupAnnouncementUrl), headers: headers)
              .timeout(timeout);

          if (response.statusCode == 200 && response.body.isNotEmpty) {
            final data = json.decode(response.body);
            // 检查公告是否激活
            if (data['is_active'] == true) {
              return Announcement.fromJson(data);
            }
          }
        }

        return null;
      } finally {
        client.close();
      }
    } catch (e) {
      print('获取公告网络请求失败: $e');
      return null;
    }
  }

  /// 获取缓存的公告
  Future<Announcement?> _getCachedAnnouncement() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cachedAnnouncementKey);
      if (cachedData != null) {
        final data = json.decode(cachedData);
        return Announcement.fromJson(data);
      }
    } catch (e) {
      print('读取缓存公告失败: $e');
    }
    return null;
  }

  /// 保存公告到缓存
  Future<void> _saveCachedAnnouncement(Announcement announcement) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(announcement.toJson());
      await prefs.setString(_cachedAnnouncementKey, data);
    } catch (e) {
      print('保存缓存公告失败: $e');
    }
  }

  /// 标记公告为已读
  Future<void> markAnnouncementAsRead() async {
    try {
      if (announcement.value != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_lastAnnouncementIdKey, announcement.value!.id);
        announcement.value = null;
        print('✅ 公告已标记为已读');
      }
    } catch (e) {
      print('标记公告已读失败: $e');
    }
  }

  /// 手动刷新公告
  Future<void> refreshAnnouncement() async {
    try {
      print('🔄 手动刷新公告...');
      await _fetchLatestAnnouncementAsync();
      
      if (announcement.value == null) {
        Get.snackbar(
          '提示', 
          '没有新的公告',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.surface,
          colorText: Get.theme.colorScheme.onSurface,
        );
      }
    } catch (e) {
      print('刷新公告失败: $e');
      Get.snackbar(
        '错误', 
        '刷新公告失败',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
        colorText: Get.theme.colorScheme.error,
      );
    }
  }

  /// 清除所有公告缓存（用于调试）
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastAnnouncementIdKey);
      await prefs.remove(_cachedAnnouncementKey);
      await prefs.remove(_lastCheckTimeKey);
      announcement.value = null;
      print('🗑️ 公告缓存已清除');
    } catch (e) {
      print('清除缓存失败: $e');
    }
  }
}

/// 公告数据模型
class Announcement {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final bool isImportant;
  final bool isActive;

  Announcement({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    this.isImportant = false,
    this.isActive = true,
  });

  /// 从JSON创建Announcement对象
  factory Announcement.fromJson(Map<String, dynamic> json) {
    return Announcement(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      date: DateTime.parse(json['created_at'] as String),
      isImportant: json['is_important'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'created_at': date.toIso8601String(),
      'is_important': isImportant,
      'is_active': isActive,
    };
  }
}
